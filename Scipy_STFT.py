# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="STFT Spectral Decomposition",
                                      category="Seismic interpretation",
                                      description="Advanced workflow for time-frequency analysis using Short-Time Fourier Transform with improved temporal resolution. Generates time-varying spectral cubes for each frequency.",
                                      authors="BKP_Team@PTM",
                                      version="1.0")

# --- Define the UI for the Prizm Workflow Runner ---

pwr_description.add_object_ref_parameter(name='seismic_id', label='Input Seismic Cube', description='Select the seismic cube to decompose', object_type=DomainObjectsEnum.Seismic3D)
pwr_description.add_string_parameter(name='output_base_name', label='Output Cube Base Name', description='Base name for the output cubes (e.g., "STFT_")', default_value='STFT_')
pwr_description.add_object_ref_parameter(name='template', label='Output Seismic Template', description='Select a template for the output attribute cubes', object_type=DomainObjectsEnum.TemplateContinuous)

pwr_description.add_integer_parameter(name='chunk_size', label='Chunk Size', description='Number of ilines to process at a time. Smaller values use less memory.', default_value=50, minimum_value=1, maximum_value=500)

pwr_description.add_string_parameter(name='frequencies', label='Frequencies (Hz)', description='Comma-separated list of frequencies to output (e.g., 10,25,40)', default_value='10,25,40')
pwr_description.add_integer_parameter(name='window_length', label='Window Length (samples)', description='FFT window length in samples. Smaller windows = better temporal resolution, larger = better frequency resolution.', default_value=64, minimum_value=16, maximum_value=512)
pwr_description.add_float_parameter(name='overlap_percent', label='Window Overlap (%)', description='Percentage overlap between consecutive windows (0-95%). Higher overlap = better temporal resolution.', default_value=75.0, minimum_value=0.0, maximum_value=95.0)
pwr_description.add_string_parameter(name='dt', label='Sample Interval (seconds)', description='Sample interval of the seismic data in seconds (e.g., 0.004)', default_value='0.004')
pwr_description.add_enum_parameter(name='window_type', label='Window Function', description='Type of window function to apply', options={0:'Hann', 1:'Hamming', 2:'Blackman', 3:'Kaiser', 4:'Tukey'}, default_value=0)
pwr_description.add_enum_parameter(name='output_mode', label='Output Mode', description='How to represent the time-frequency data in the output cube', options={0:'Peak Amplitude', 1:'RMS Amplitude', 2:'Instantaneous Amplitude', 3:'Time-Weighted Average'}, default_value=2)
pwr_description.add_boolean_parameter(name='preserve_phase', label='Preserve Phase Information', description='Output complex-valued results to preserve phase (creates real and imaginary cubes)', default_value=False)

# End: PWR Description


from cegalprizm.pythontool import *
import numpy as np
from scipy.signal import stft, get_window
from scipy.signal.windows import hann, hamming, blackman, kaiser, tukey

def parse_float_list(s):
    try:
        return [float(item.strip()) for item in s.split(',')]
    except ValueError:
        raise ValueError("Frequencies must be a comma-separated list of numbers.")

def get_window_function(window_type, window_length):
    """Get the appropriate window function based on user selection"""
    window_functions = {
        0: lambda n: hann(n),           # Hann
        1: lambda n: hamming(n),        # Hamming
        2: lambda n: blackman(n),       # Blackman
        3: lambda n: kaiser(n, beta=8.6), # Kaiser (beta=8.6 for good sidelobe suppression)
        4: lambda n: tukey(n, alpha=0.25) # Tukey (25% taper)
    }
    return window_functions.get(window_type, lambda n: hann(n))(window_length)

def compute_time_varying_amplitude(stft_result, mode):
    """Compute amplitude representation based on selected mode"""
    amplitudes = np.abs(stft_result)

    if mode == 0:  # Peak Amplitude
        return np.max(amplitudes, axis=1)  # Max over time
    elif mode == 1:  # RMS Amplitude
        return np.sqrt(np.mean(amplitudes**2, axis=1))  # RMS over time
    elif mode == 2:  # Instantaneous Amplitude (preserve time variation)
        return amplitudes  # Keep full time-frequency matrix
    elif mode == 3:  # Time-Weighted Average
        # Weight recent samples more heavily
        time_weights = np.linspace(0.5, 1.0, amplitudes.shape[1])
        return np.average(amplitudes, axis=1, weights=time_weights)
    else:
        return amplitudes  # Default to instantaneous

# --- 1. Connect to Petrel and retrieve user parameters ---
petrel = PetrelConnection(allow_experimental=True)
print('PetrelConnection established')

petrel_objects = petrel.get_petrelobjects_by_guids([parameters['seismic_id'], parameters['template']])
selected_seismic_cube = petrel_objects[0]
if selected_seismic_cube is None: raise ValueError("No input seismic cube has been selected.")
selected_seismic_template = petrel_objects[1]
if selected_seismic_template is None: raise ValueError("No output template has been selected.")

output_base_name = parameters['output_base_name']
if not output_base_name: raise ValueError("An output cube base name must be provided.")

chunk_size = parameters['chunk_size']
target_frequencies = parse_float_list(parameters['frequencies'])
window_length = parameters['window_length']
overlap_percent = parameters['overlap_percent']
dt = float(parameters['dt'])
fs = 1.0 / dt  # Calculate sampling frequency
window_type = parameters['window_type']
output_mode = parameters['output_mode']
preserve_phase = parameters['preserve_phase']

# Calculate overlap in samples
overlap_samples = int(window_length * overlap_percent / 100.0)

print(f"Target Frequencies: {target_frequencies} Hz")
print(f"Sampling Frequency: {fs} Hz")
print(f"Window Length: {window_length} samples")
print(f"Overlap: {overlap_samples} samples ({overlap_percent}%)")
print(f"Window Type: {['Hann', 'Hamming', 'Blackman', 'Kaiser', 'Tukey'][window_type]}")
print(f"Output Mode: {['Peak Amplitude', 'RMS Amplitude', 'Instantaneous Amplitude', 'Time-Weighted Average'][output_mode]}")

# Get the window function
window = get_window_function(window_type, window_length)

# --- 2. Prepare output cubes in Petrel ---
output_cubes = []
phase_cubes = []  # For complex output if preserve_phase is True

for freq in target_frequencies:
    # Create amplitude cube
    cube_name = f"{output_base_name}{freq}Hz"
    print(f"Creating output cube: {cube_name}")
    new_cube = selected_seismic_cube.clone(cube_name, copy_values=False, template=selected_seismic_template)
    output_cubes.append(new_cube)

    # Create phase cube if preserving phase information
    if preserve_phase:
        phase_cube_name = f"{output_base_name}{freq}Hz_Phase"
        print(f"Creating phase cube: {phase_cube_name}")
        phase_cube = selected_seismic_cube.clone(phase_cube_name, copy_values=False, template=selected_seismic_template)
        phase_cubes.append(phase_cube)

# --- 3. Process the data in chunks ---
def get_chunks(extent, chunk_size):
    i_range, j_range, k_range = extent
    i_chunks = [(start, min(start + chunk_size, i_range - 1)) for start in range(0, i_range, chunk_size)]
    return [(i, (0, j_range - 1), (0, k_range - 1)) for i in i_chunks]

def interpolate_to_time_samples(stft_amplitudes, stft_times, original_samples, dt):
    """Interpolate STFT results back to original time sampling"""
    original_times = np.arange(original_samples) * dt
    stft_sample_times = stft_times

    # Interpolate each frequency component
    interpolated = np.zeros((stft_amplitudes.shape[0], original_samples))
    for freq_idx in range(stft_amplitudes.shape[0]):
        interpolated[freq_idx, :] = np.interp(original_times, stft_sample_times, stft_amplitudes[freq_idx, :])

    return interpolated

cube_dimensions = selected_seismic_cube.extent
chunks = get_chunks(cube_dimensions, chunk_size)
print(f"Starting processing of {len(chunks)} chunks...")

for i, chunk in enumerate(chunks):
    i_range, j_range, k_range = chunk
    print(f"Processing chunk {i + 1}/{len(chunks)} (Ilines: {i_range[0]}-{i_range[1]})...")

    seismic_array_3d = selected_seismic_cube.chunk(i_range, j_range, k_range).as_array()
    original_shape = seismic_array_3d.shape
    n_samples = original_shape[2]

    # Reshape to (traces, samples) for processing
    traces_2d = seismic_array_3d.reshape(-1, n_samples)

    # Initialize arrays to hold results for this chunk
    chunk_results = np.zeros((traces_2d.shape[0], len(target_frequencies), n_samples))
    if preserve_phase:
        chunk_phase_results = np.zeros((traces_2d.shape[0], len(target_frequencies), n_samples))

    # Process each trace individually
    for trace_idx, trace in enumerate(traces_2d):
        # Perform STFT on the trace with improved parameters
        f, t, Zxx = stft(trace, fs=fs, window=window, nperseg=window_length,
                        noverlap=overlap_samples, return_onesided=True,
                        boundary='zeros', padded=True)

        # Find the frequency indices closest to our targets
        freq_indices = [np.argmin(np.abs(f - target_f)) for target_f in target_frequencies]

        # Extract and process results for each target frequency
        for freq_idx, target_idx in enumerate(freq_indices):
            # Get complex STFT values for this frequency
            freq_stft = Zxx[target_idx, :]

            # Compute amplitude based on selected mode
            if output_mode == 2:  # Instantaneous Amplitude - preserve time variation
                amplitudes = np.abs(freq_stft)
                # Interpolate back to original time sampling for better temporal resolution
                interpolated_amps = np.interp(np.arange(n_samples) * dt, t, amplitudes)
                chunk_results[trace_idx, freq_idx, :] = interpolated_amps
            else:
                # For other modes, compute single value per trace
                amplitudes = compute_time_varying_amplitude(freq_stft.reshape(1, -1), output_mode)
                chunk_results[trace_idx, freq_idx, :] = amplitudes[0]  # Broadcast to all samples

            # Store phase information if requested
            if preserve_phase:
                phases = np.angle(freq_stft)
                interpolated_phases = np.interp(np.arange(n_samples) * dt, t, phases)
                chunk_phase_results[trace_idx, freq_idx, :] = interpolated_phases

    # --- 4. Write results back to Petrel ---
    for freq_idx, cube in enumerate(output_cubes):
        # Reshape the results for this frequency back to the cube's 3D shape
        frequency_data = chunk_results[:, freq_idx, :].reshape(original_shape)

        # Write the data to the Petrel chunk
        output_chunk = cube.chunk(i_range, j_range, k_range)
        output_chunk.set(frequency_data.astype(np.float32))

        # Write phase data if preserving phase
        if preserve_phase and freq_idx < len(phase_cubes):
            phase_data = chunk_phase_results[:, freq_idx, :].reshape(original_shape)
            phase_chunk = phase_cubes[freq_idx].chunk(i_range, j_range, k_range)
            phase_chunk.set(phase_data.astype(np.float32))

print('Enhanced STFT spectral decomposition successfully created for all frequencies.')
if preserve_phase:
    print('Phase information preserved in separate cubes.')
print(f'Temporal resolution improved with {overlap_percent}% window overlap.')