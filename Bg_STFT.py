# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="SciPy Spectral Decomposition",
                                      category="Seismic interpretation",
                                      description="A robust workflow to calculate frequency spectra for a seismic cube using the SciPy library. This generates a separate output cube for each specified frequency.",
                                      authors="author@company",
                                      version="2.0")

# --- Define the UI for the Prizm Workflow Runner ---

pwr_description.add_object_ref_parameter(name='seismic_id', label='Input Seismic Cube', description='Select the seismic cube to decompose', object_type=DomainObjectsEnum.Seismic3D)
pwr_description.add_string_parameter(name='output_base_name', label='Output Cube Base Name', description='Base name for the output cubes (e.g., "SpecDecomp_")')
pwr_description.add_object_ref_parameter(name='template', label='Output Seismic Template', description='Select a template for the output attribute cubes', object_type=DomainObjectsEnum.TemplateContinuous)

pwr_description.add_integer_parameter(name='chunk_size', label='Chunk Size', description='Number of ilines to process at a time. Smaller values use less memory.', default_value=50, minimum_value=1, maximum_value=500)

pwr_description.add_string_parameter(name='frequencies', label='Frequencies (Hz)', description='Comma-separated list of frequencies to output (e.g., 10,25,40)', default_value='10,25,40')
pwr_description.add_integer_parameter(name='window_length', label='Window Length (samples)', description='The length of the FFT window in samples. A larger window resolves lower frequencies better.', default_value=128, minimum_value=16)
pwr_description.add_string_parameter(name='dt', label='Sample Interval (seconds)', description='Sample interval of the seismic data in seconds (e.g., 0.004)', default_value='0.004')
pwr_description.add_boolean_parameter(name='normalize', label='Normalize Energy', description='Normalize the energy within each STFT window', default_value=False)

# End: PWR Description


from cegalprizm.pythontool import *
import numpy as np
from scipy.signal import stft

def parse_float_list(s):
    try:
        return [float(item.strip()) for item in s.split(',')]
    except ValueError:
        raise ValueError("Frequencies must be a comma-separated list of numbers.")

# --- 1. Connect to Petrel and retrieve user parameters ---
petrel = PetrelConnection(allow_experimental=True)
print('PetrelConnection established')

petrel_objects = petrel.get_petrelobjects_by_guids([parameters['seismic_id'], parameters['template']])
selected_seismic_cube = petrel_objects[0]
if selected_seismic_cube is None: raise ValueError("No input seismic cube has been selected.")
selected_seismic_template = petrel_objects[1]
if selected_seismic_template is None: raise ValueError("No output template has been selected.")

output_base_name = parameters['output_base_name']
if not output_base_name: raise ValueError("An output cube base name must be provided.")

chunk_size = parameters['chunk_size']
target_frequencies = parse_float_list(parameters['frequencies'])
window_length = parameters['window_length']
dt = float(parameters['dt'])
fs = 1.0 / dt  # Calculate sampling frequency
normalize = parameters['normalize']

print(f"Target Frequencies: {target_frequencies} Hz")
print(f"Sampling Frequency: {fs} Hz")

# --- 2. Prepare output cubes in Petrel ---
output_cubes = []
for freq in target_frequencies:
    cube_name = f"{output_base_name}{freq}Hz"
    print(f"Creating output cube: {cube_name}")
    new_cube = selected_seismic_cube.clone(cube_name, copy_values=False, template=selected_seismic_template)
    output_cubes.append(new_cube)

# --- 3. Process the data in chunks ---
def get_chunks(extent, chunk_size):
    i_range, j_range, k_range = extent
    i_chunks = [(start, min(start + chunk_size, i_range - 1)) for start in range(0, i_range, chunk_size)]
    return [(i, (0, j_range - 1), (0, k_range - 1)) for i in i_chunks]

cube_dimensions = selected_seismic_cube.extent
chunks = get_chunks(cube_dimensions, chunk_size)
print(f"Starting processing of {len(chunks)} chunks...")

for i, chunk in enumerate(chunks):
    i_range, j_range, k_range = chunk
    print(f"Processing chunk {i + 1}/{len(chunks)} (Ilines: {i_range[0]}-{i_range[1]})...")
    
    seismic_array_3d = selected_seismic_cube.chunk(i_range, j_range, k_range).as_array()
    original_shape = seismic_array_3d.shape
    n_samples = original_shape[2]

    # Reshape to (traces, samples) for processing
    traces_2d = seismic_array_3d.reshape(-1, n_samples)

    # Initialize an array to hold all results for this chunk
    chunk_results = np.zeros((traces_2d.shape[0], len(target_frequencies)))

    # Process each trace individually
    for trace_idx, trace in enumerate(traces_2d):
        # Perform STFT on the trace
        f, t, Zxx = stft(trace, fs=fs, nperseg=window_length, noverlap=window_length // 2 if normalize else 0)
        
        # Get the magnitude
        amplitudes = np.abs(Zxx)
        
        # Find the frequency indices closest to our targets
        freq_indices = [np.argmin(np.abs(f - target_f)) for target_f in target_frequencies]
        
        # Sum the amplitudes over time for each target frequency
        for freq_idx, target_idx in enumerate(freq_indices):
            chunk_results[trace_idx, freq_idx] = np.sum(amplitudes[target_idx, :])

    # --- 4. Write results back to Petrel ---
    for freq_idx, cube in enumerate(output_cubes):
        # Reshape the results for this frequency back to the cube's 3D shape
        frequency_data = chunk_results[:, freq_idx].reshape(original_shape[0], original_shape[1])
        
        # The result of STFT is 2D (ilines, xlines), but Petrel expects 3D. We broadcast it across the time/sample axis.
        output_array_3d = np.zeros(original_shape, dtype=np.float32)
        output_array_3d[:, :, :] = frequency_data[:, :, np.newaxis]
        
        # Write the data to the Petrel chunk
        output_chunk = cube.chunk(i_range, j_range, k_range)
        output_chunk.set(output_array_3d)

print('Spectral decomposition successfully created for all frequencies.')