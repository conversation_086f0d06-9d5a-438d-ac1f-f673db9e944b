import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection, Well, WellLog, GlobalWellLog, DiscreteGlobalWellLog

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

# The class WorkflowDescription is used to define the Cegal Prizm workflow. It is assigned to a Python variable called 'pwr_description' 
pwr_description = WorkflowDescription(name="Vsh Calculator",
                                      category="Bundled workflows",
                                      description="Calculate Vsh using different algorithms",
                                      authors="author@company",
                                      version="1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.

pwr_description.add_object_ref_parameter(name = "well_id", label = "Select well", description = "Select the well", object_type = DomainObjectsEnum.Well)
pwr_description.add_object_ref_parameter(name = "log_id", label = "Gamma ray input log", description = "Gamma ray log used to calculate Vshale", object_type = DomainObjectsEnum.WellContinuousLog, template_type = 'GammaRay', linked_input_name = "well_id")
pwr_description.add_integer_parameter(name = 'grs_input', label = 'Clean rock GR value:', description = 'The GammaRay value associated with a clean reservoir having no shale ', default_value = 15, minimum_value = 1, maximum_value = 1000)
pwr_description.add_integer_parameter(name = 'grsh_input', label = 'Shale GammaRay value', description = 'The GammaRay value associated with a zone of 100% shale', default_value = 120, minimum_value = 1, maximum_value = 1000)
pwr_description.add_enum_parameter(name = 'methods', label = 'Select method:', description = 'Choose which method you want to use for calculation Vsh', options = {0: 'Linear', 1: 'Larionov - tertiary rocks', 2: 'Larionov - older rocks', 3: 'Clavier', 4: 'Stieber'})
pwr_description.add_string_parameter(name = 'suffix', label = 'Name for new vshale log', description = 'Add a name for the new vshale logs to be created', default_value = 'vshale_pwr')

# End: PWR Description

# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()

print("Establishing PetrelConnection") 
petrel = PetrelConnection(allow_experimental=True)
print("Connected to {}".format(petrel.get_current_project_name()))



#############################
# Checks for input data
#############################

# Retrieve the selected well using its GUID and check if it's a valid Well object
user_well = petrel.get_petrelobjects_by_guids([parameters['well_id']])[0]
if not isinstance(user_well, Well):
    raise AssertionError("No well selected")
print("Retrieved well by guid")

# Retrieve the selected gamma ray log using its GUID and check if it's a valid WellLog object
gr_log = petrel.get_petrelobjects_by_guids([parameters['log_id']])[0]
if not isinstance(gr_log, WellLog):
    raise AssertionError("No gamma ray log selected as input")
print("Retrieved gamma ray input log by guid")

# Retrieve the selected method and input values for the Vsh calculation
method = parameters['methods']
GRS_input = parameters['grs_input']
GRSh_input = parameters['grsh_input']

# Convert the gamma ray log to a Pandas DataFrame and check if it's not empty
df = gr_log.as_dataframe()
if len(df) == 0:
    raise AssertionError("Gamma ray log is empty. Creation of DataFrame failed")
print("Created Pandas DataFrame from gamma ray input log")



######################################
# Functions for identifying sequences
######################################

# Function to calculate shale volume using the selected method
def calculate_vshale(method, GRS_input, GRSh_input):

    # Calculate the Gamma Ray Index
    GR_index = (df['Value'] - GRS_input) / (GRSh_input - GRS_input)
    GR_index = np.where(GR_index < 0, 0, GR_index)
    GR_index = np.where(GR_index > 1, 1, GR_index)

    # Calculate Vshale based on the selected method
    if method == 0:
        vshale = GR_index
    elif method == 1:
        vshale = 0.083 * (2 ** (3.7 * GR_index) - 1.0)
    elif method == 2:
        vshale = 0.33 * (2 ** (2.0 * GR_index) - 1.0)
    elif method == 3:
        vshale = 1.7 - np.sqrt(3.38 - (GR_index + 0.7) ** 2)
    elif method == 4:
        vshale = GR_index / (3 - 2 * GR_index)
    else:
        vshale = pd.Series([])

    # Ensure Vshale values are within the range 0 to 1
    vshale = np.where(vshale < 0, 0, vshale)  # Set lower limit to 0
    vshale = np.where(vshale > 1, 1, vshale)
    return vshale

# Calculate Vshale using the selected method and add it to the DataFrame
vshale = calculate_vshale(method, GRS_input, GRSh_input)
df['VSH'] = vshale
print("Calculated Vshale values")

# Assign the MD values to a variable
md = df['MD'].to_list()
# Assign the Vsh values to a variable
values = df['VSH'].to_list()



#############################
# Write back to Petrel
#############################

def write_back_log(log_name, log_to_clone, well, md, values, template):

    # Returns list of a given petrel domain object in case of duplicate names
    def get_object_list(x):
        object_list = []
        for i in x:
            if not isinstance(i, list):
                object_list.append(i)
            if isinstance(i, list):
                object_list.extend(i)
        return object_list

    if type(log_to_clone) is DiscreteGlobalWellLog:
        gwl = [i for i in get_object_list(petrel.discrete_global_well_logs) if i.petrel_name == log_name]
    if type(log_to_clone) is GlobalWellLog:
        gwl = [i for i in get_object_list(petrel.global_well_logs) if i.petrel_name == log_name]

    # Find if discrete global well log already exists. If not, create a new one.
    if len(gwl) == 0:
        print('Creating new global well log')
        global_log = log_to_clone.clone(name_of_clone = log_name, template = template)
        gwl = [global_log]

    # Check if well has this log
    well_log = [i for i in get_object_list(well.logs) if i.petrel_name == log_name]

    # If well has well log, overwrite values
    if len(well_log) == 1:
        well_log[0].readonly = False
        well_log[0].set_values(md, values)
        print(f"Values for {log_name} overwritten for {well.petrel_name}")

    # If well does not have this log, create new well log and set values to it.    
    if len(well_log) == 0:
        print('Creating new discete well log')
        well_log = gwl[0].create_well_log(well)
        well_log.readonly = False
        well_log.set_values(md, values)
        print(f"New well log {log_name} created for well {well.petrel_name}")

# Get inputs to write back function, including VShale template
log_name = gr_log.petrel_name
log_to_clone = [i for i in petrel.global_well_logs if i.petrel_name == gr_log.petrel_name][0]
template = [i for i in petrel.templates if i.petrel_name == 'VShale'][0]

write_back_log(log_name = parameters['suffix'], 
               log_to_clone = log_to_clone, 
               well = user_well, 
               md = md, 
               values = values,
               template = template)